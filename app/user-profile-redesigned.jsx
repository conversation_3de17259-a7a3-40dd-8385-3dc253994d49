import React, { useState, useRef, useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Animated,
  Platform,
  StatusBar,
  Image,
  Switch
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import CustomStatusBar from './components/CustomStatusBar';
import { useUser } from './context/UserContext';

const UserProfile = () => {
  const router = useRouter();
  const { userData, logout } = useUser();
  const fadeAnim = useRef(new Animated.Value(0)).current;

  // Settings state
  const [twoFactorAuth, setTwoFactorAuth] = useState(true);
  const [dataSharing, setDataSharing] = useState(false);
  const [notifications, setNotifications] = useState(true);
  const [darkMode, setDarkMode] = useState(false);

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 600,
      useNativeDriver: true,
    }).start();
  }, []);

  const handleLogout = () => {
    logout();
    router.replace('/sign-in');
  };

  const menuItems = [
    {
      id: 'app-settings',
      title: 'App settings',
      icon: '⚙️',
      onPress: () => router.push('/app-settings'),
      showArrow: true
    },
    {
      id: 'transaction-history',
      title: 'Transaction History',
      icon: '🕒',
      onPress: () => router.push('/transaction-history'),
      showArrow: true
    },
    {
      id: 'help-support',
      title: 'Help & Support',
      icon: '❓',
      onPress: () => router.push('/help-support'),
      showArrow: true
    },
    {
      id: 'privacy-policy',
      title: 'Privacy Policy',
      icon: '📄',
      onPress: () => router.push('/privacy-policy'),
      showArrow: true
    },
    {
      id: 'terms-conditions',
      title: 'Terms & Conditions',
      icon: '📋',
      onPress: () => router.push('/terms-conditions'),
      showArrow: true
    },
    {
      id: 'about',
      title: 'About',
      icon: 'ℹ️',
      onPress: () => router.push('/about'),
      showArrow: true
    }
  ];

  return (
    <View style={styles.container}>
      <CustomStatusBar backgroundColor="transparent" barStyle="light-content" />
      
      <SafeAreaView style={styles.safeArea}>
        {/* Header with Gradient */}
        <LinearGradient
          colors={['#7BA05B', '#9BC76D']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.headerGradient}
        >
          <View style={styles.headerContent}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <Text style={styles.backButtonIcon}>←</Text>
              <Text style={styles.backButtonText}>Back</Text>
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Profile & Settings</Text>
            <View style={styles.headerSpacer} />
          </View>
        </LinearGradient>

        <ScrollView 
          contentContainerStyle={styles.scrollContent} 
          showsVerticalScrollIndicator={false}
        >
          {/* Profile Section */}
          <Animated.View
            style={[
              styles.profileSection,
              { opacity: fadeAnim }
            ]}
          >
            <View style={styles.profileImageContainer}>
              {userData.profileImage ? (
                <Image source={userData.profileImage} style={styles.profileImage} />
              ) : (
                <View style={styles.profileImagePlaceholder}>
                  <Text style={styles.profileInitial}>
                    {(userData.firstName || 'L').charAt(0)}
                  </Text>
                </View>
              )}
            </View>

            <Text style={styles.userName}>
              {userData.hidePersonalInfo 
                ? 'LANCE BAUSTISTA' 
                : `${userData.firstName || 'LANCE'} ${userData.lastName || 'BAUSTISTA'}`
              }
            </Text>
            
            <View style={styles.userIdContainer}>
              <Text style={styles.userIdLabel}>
                ID: {userData.hidePersonalInfo ? '2021*******' : (userData.controlNumber || '2021*******')}
              </Text>
              <TouchableOpacity style={styles.eyeButton}>
                <Text style={styles.eyeIcon}>👁️</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.verifiedBadge}>
              <Text style={styles.verifiedIcon}>✓</Text>
              <Text style={styles.verifiedText}>Verified</Text>
            </View>

            <TouchableOpacity 
              style={styles.editProfileButton}
              onPress={() => router.push('/edit-profile')}
            >
              <Text style={styles.editProfileButtonText}>Edit Profile</Text>
            </TouchableOpacity>
          </Animated.View>

          {/* Menu Items */}
          <Animated.View
            style={[
              styles.menuSection,
              { opacity: fadeAnim }
            ]}
          >
            {menuItems.map((item, index) => (
              <TouchableOpacity
                key={item.id}
                style={[
                  styles.menuItem,
                  index === menuItems.length - 1 && styles.lastMenuItem
                ]}
                onPress={item.onPress}
              >
                <View style={styles.menuItemLeft}>
                  <Text style={styles.menuItemIcon}>{item.icon}</Text>
                  <Text style={styles.menuItemTitle}>{item.title}</Text>
                </View>
                {item.showArrow && (
                  <Text style={styles.menuItemArrow}>›</Text>
                )}
              </TouchableOpacity>
            ))}
          </Animated.View>

          {/* Logout Button */}
          <Animated.View
            style={[
              styles.logoutSection,
              { opacity: fadeAnim }
            ]}
          >
            <TouchableOpacity 
              style={styles.logoutButton}
              onPress={handleLogout}
            >
              <Text style={styles.logoutButtonText}>Log Out</Text>
            </TouchableOpacity>
          </Animated.View>

          {/* Version Info */}
          <View style={styles.versionSection}>
            <Text style={styles.versionText}>MentalEase v1.0.0</Text>
            <Text style={styles.copyrightText}>© 2023 MentalEase Inc.</Text>
          </View>
        </ScrollView>
      </SafeAreaView>
    </View>
  );
};

export default UserProfile;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  safeArea: {
    flex: 1,
  },
  headerGradient: {
    paddingTop: Platform.OS === 'ios' ? 50 : StatusBar.currentHeight + 20,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButtonIcon: {
    fontSize: 20,
    color: '#FFFFFF',
    marginRight: 5,
  },
  backButtonText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  headerTitle: {
    fontSize: 18,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  headerSpacer: {
    width: 60,
  },
  scrollContent: {
    padding: 20,
    paddingBottom: 100,
  },
  profileSection: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 30,
    alignItems: 'center',
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  profileImageContainer: {
    marginBottom: 20,
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  profileImagePlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#D4A574',
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileInitial: {
    fontSize: 40,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  userName: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 8,
    textAlign: 'center',
  },
  userIdContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  userIdLabel: {
    fontSize: 14,
    color: '#666666',
    marginRight: 8,
  },
  eyeButton: {
    padding: 4,
  },
  eyeIcon: {
    fontSize: 16,
  },
  verifiedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E8F5E8',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
    marginBottom: 25,
  },
  verifiedIcon: {
    fontSize: 14,
    color: '#4CAF50',
    marginRight: 5,
  },
  verifiedText: {
    fontSize: 14,
    color: '#4CAF50',
    fontWeight: '600',
  },
  editProfileButton: {
    backgroundColor: '#9BC76D',
    borderRadius: 25,
    paddingVertical: 12,
    paddingHorizontal: 30,
  },
  editProfileButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  menuSection: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 18,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  lastMenuItem: {
    borderBottomWidth: 0,
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuItemIcon: {
    fontSize: 20,
    marginRight: 15,
  },
  menuItemTitle: {
    fontSize: 16,
    color: '#333333',
    fontWeight: '500',
  },
  menuItemArrow: {
    fontSize: 20,
    color: '#7BA05B',
    fontWeight: 'bold',
  },
  logoutSection: {
    marginBottom: 30,
  },
  logoutButton: {
    backgroundColor: '#FF6B6B',
    borderRadius: 25,
    paddingVertical: 15,
    alignItems: 'center',
    shadowColor: '#FF6B6B',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  logoutButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  versionSection: {
    alignItems: 'center',
    marginTop: 20,
  },
  versionText: {
    fontSize: 14,
    color: '#999999',
    marginBottom: 5,
  },
  copyrightText: {
    fontSize: 14,
    color: '#999999',
  },
});
