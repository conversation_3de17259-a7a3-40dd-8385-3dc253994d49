import React, { useState, useRef, useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Animated,
  Platform,
  StatusBar,
  Switch
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import CustomStatusBar from './components/CustomStatusBar';

const AppSettings = () => {
  const router = useRouter();
  const fadeAnim = useRef(new Animated.Value(0)).current;

  // Settings state
  const [twoFactorAuth, setTwoFactorAuth] = useState(true);
  const [dataSharing, setDataSharing] = useState(false);
  const [notifications, setNotifications] = useState(true);
  const [darkMode, setDarkMode] = useState(false);
  const [language, setLanguage] = useState('English');

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 600,
      useNativeDriver: true,
    }).start();
  }, []);

  const securitySettings = [
    {
      id: 'change-password',
      title: 'Change Password',
      onPress: () => router.push('/change-password'),
      showArrow: true
    },
    {
      id: 'two-factor',
      title: 'Two-Factor Authentication',
      value: twoFactorAuth,
      onToggle: setTwoFactorAuth,
      isToggle: true
    },
    {
      id: 'data-sharing',
      title: 'Data Sharing',
      value: dataSharing,
      onToggle: setDataSharing,
      isToggle: true
    }
  ];

  const appSharingSettings = [
    {
      id: 'notifications',
      title: 'Notifications',
      value: notifications,
      onToggle: setNotifications,
      isToggle: true
    },
    {
      id: 'dark-mode',
      title: 'Dark mode',
      value: darkMode,
      onToggle: setDarkMode,
      isToggle: true
    },
    {
      id: 'language',
      title: 'Language',
      value: language,
      onPress: () => router.push('/language-settings'),
      showArrow: true
    }
  ];

  const renderSettingItem = (item) => (
    <View key={item.id} style={styles.settingItem}>
      <Text style={styles.settingTitle}>{item.title}</Text>
      {item.isToggle ? (
        <Switch
          value={item.value}
          onValueChange={item.onToggle}
          trackColor={{ false: '#E0E0E0', true: '#7BA05B' }}
          thumbColor={item.value ? '#FFFFFF' : '#FFFFFF'}
          ios_backgroundColor="#E0E0E0"
        />
      ) : (
        <TouchableOpacity onPress={item.onPress} style={styles.settingAction}>
          {item.value && typeof item.value === 'string' && (
            <Text style={styles.settingValue}>{item.value}</Text>
          )}
          {item.showArrow && (
            <Text style={styles.settingArrow}>›</Text>
          )}
        </TouchableOpacity>
      )}
    </View>
  );

  return (
    <View style={styles.container}>
      <CustomStatusBar backgroundColor="transparent" barStyle="light-content" />
      
      <SafeAreaView style={styles.safeArea}>
        {/* Header with Gradient */}
        <LinearGradient
          colors={['#7BA05B', '#9BC76D']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.headerGradient}
        >
          <View style={styles.headerContent}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <Text style={styles.backButtonIcon}>←</Text>
              <Text style={styles.backButtonText}>Back</Text>
            </TouchableOpacity>
            <Text style={styles.headerTitle}>App settings</Text>
            <View style={styles.headerSpacer} />
          </View>
        </LinearGradient>

        <ScrollView 
          contentContainerStyle={styles.scrollContent} 
          showsVerticalScrollIndicator={false}
        >
          {/* Security Section */}
          <Animated.View
            style={[
              styles.section,
              { opacity: fadeAnim }
            ]}
          >
            <Text style={styles.sectionTitle}>Security</Text>
            <View style={styles.sectionContent}>
              {securitySettings.map(renderSettingItem)}
            </View>
          </Animated.View>

          {/* App Sharing Section */}
          <Animated.View
            style={[
              styles.section,
              { opacity: fadeAnim }
            ]}
          >
            <Text style={styles.sectionTitle}>App Sharing</Text>
            <View style={styles.sectionContent}>
              {appSharingSettings.map(renderSettingItem)}
            </View>
          </Animated.View>

          {/* Version Info */}
          <View style={styles.versionSection}>
            <Text style={styles.versionText}>MentalEase v1.0.0</Text>
            <Text style={styles.copyrightText}>© 2023 MentalEase Inc.</Text>
          </View>
        </ScrollView>
      </SafeAreaView>
    </View>
  );
};

export default AppSettings;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  safeArea: {
    flex: 1,
  },
  headerGradient: {
    paddingTop: Platform.OS === 'ios' ? 50 : StatusBar.currentHeight + 20,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButtonIcon: {
    fontSize: 20,
    color: '#FFFFFF',
    marginRight: 5,
  },
  backButtonText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  headerTitle: {
    fontSize: 18,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  headerSpacer: {
    width: 60,
  },
  scrollContent: {
    padding: 20,
    paddingBottom: 100,
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 15,
  },
  sectionContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 18,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  settingTitle: {
    fontSize: 16,
    color: '#333333',
    fontWeight: '500',
    flex: 1,
  },
  settingAction: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingValue: {
    fontSize: 16,
    color: '#666666',
    marginRight: 10,
  },
  settingArrow: {
    fontSize: 20,
    color: '#7BA05B',
    fontWeight: 'bold',
  },
  versionSection: {
    alignItems: 'center',
    marginTop: 40,
  },
  versionText: {
    fontSize: 14,
    color: '#999999',
    marginBottom: 5,
  },
  copyrightText: {
    fontSize: 14,
    color: '#999999',
  },
});
