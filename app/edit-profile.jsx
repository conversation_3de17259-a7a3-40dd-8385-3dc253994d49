import React, { useState, useRef, useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Animated,
  Platform,
  StatusBar
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import CustomStatusBar from './components/CustomStatusBar';
import { useUser } from './context/UserContext';

const EditProfile = () => {
  const router = useRouter();
  const { userData } = useUser();
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 600,
      useNativeDriver: true,
    }).start();
  }, []);

  const personalInfo = [
    { label: 'First Name', value: userData.firstName || 'Lance' },
    { label: 'Middle Name', value: userData.middleName || 'N/A' },
    { label: 'Last Name', value: userData.lastName || 'Bautista' },
    { label: 'Age', value: userData.age || '22' },
    { label: 'Gender', value: userData.gender || 'Male' },
    { label: 'Civil Status', value: userData.civilStatus || 'Single' }
  ];

  const contactInfo = [
    { label: 'Email', value: userData.email || '<EMAIL>' },
    { label: 'Phone', value: userData.phone || '0123456789' },
    { label: 'Address', value: userData.address || '1234 San Isidro St. Sampaloc, Manila' }
  ];

  const backgroundInfo = [
    { label: 'Birthdate', value: userData.birthdate || '03-25-2005' },
    { label: 'Birthplace', value: userData.birthplace || 'Manila' },
    { label: 'Religion', value: userData.religion || 'Catholic' }
  ];

  const renderInfoSection = (title, data) => (
    <View style={styles.infoSection}>
      <Text style={styles.sectionTitle}>{title}</Text>
      <View style={styles.sectionContent}>
        {data.map((item, index) => (
          <View 
            key={index} 
            style={[
              styles.infoItem,
              index === data.length - 1 && styles.lastInfoItem
            ]}
          >
            <Text style={styles.infoLabel}>{item.label}</Text>
            <Text style={styles.infoValue}>{item.value}</Text>
          </View>
        ))}
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <CustomStatusBar backgroundColor="transparent" barStyle="light-content" />
      
      <SafeAreaView style={styles.safeArea}>
        {/* Header with Gradient */}
        <LinearGradient
          colors={['#7BA05B', '#9BC76D']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.headerGradient}
        >
          <View style={styles.headerContent}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <Text style={styles.backButtonIcon}>←</Text>
              <Text style={styles.backButtonText}>Back</Text>
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Edit Profile</Text>
            <View style={styles.headerSpacer} />
          </View>
        </LinearGradient>

        <ScrollView 
          contentContainerStyle={styles.scrollContent} 
          showsVerticalScrollIndicator={false}
        >
          {/* Personal Information */}
          <Animated.View
            style={[
              styles.section,
              { opacity: fadeAnim }
            ]}
          >
            <Text style={styles.mainSectionTitle}>Personal Information</Text>
            
            {renderInfoSection('Basic Information', personalInfo)}
            {renderInfoSection('Contact Information', contactInfo)}
            {renderInfoSection('Background Information', backgroundInfo)}

            <TouchableOpacity 
              style={styles.editButton}
              onPress={() => {
                // Handle edit functionality
                console.log('Edit information pressed');
              }}
            >
              <Text style={styles.editButtonText}>Edit Information</Text>
            </TouchableOpacity>
          </Animated.View>
        </ScrollView>
      </SafeAreaView>
    </View>
  );
};

export default EditProfile;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  safeArea: {
    flex: 1,
  },
  headerGradient: {
    paddingTop: Platform.OS === 'ios' ? 50 : StatusBar.currentHeight + 20,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButtonIcon: {
    fontSize: 20,
    color: '#FFFFFF',
    marginRight: 5,
  },
  backButtonText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  headerTitle: {
    fontSize: 18,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  headerSpacer: {
    width: 60,
  },
  scrollContent: {
    padding: 20,
    paddingBottom: 100,
  },
  section: {
    flex: 1,
  },
  mainSectionTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 25,
  },
  infoSection: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 15,
  },
  sectionContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  infoItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 18,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  lastInfoItem: {
    borderBottomWidth: 0,
  },
  infoLabel: {
    fontSize: 16,
    color: '#333333',
    fontWeight: '500',
    flex: 1,
  },
  infoValue: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'right',
    flex: 1,
  },
  editButton: {
    backgroundColor: '#7BA05B',
    borderRadius: 25,
    paddingVertical: 15,
    alignItems: 'center',
    marginTop: 20,
    shadowColor: '#7BA05B',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  editButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
